import 'dart:async';
import 'dart:collection';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:path/path.dart';

import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:synchronized/synchronized.dart';
import '../models/event.dart';
import '../models/category.dart';
import '../models/product.dart';
import '../models/seller.dart';
import '../models/prepayment.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/prepayment_product_link.dart';
import '../models/sales_log.dart';
import '../repositories/set_discount_transaction_repository.dart';
import '../utils/database_optimizer.dart';
import '../utils/logger_utils.dart';

/// 데이터베이스 서비스 인터페이스입니다.
/// - DB 인스턴스 접근을 위한 getter만 정의(구현은 DatabaseServiceImpl)
abstract class DatabaseService {
  Future<Database> get database;
  Future<void> deleteDatabase();
  Future<T> safeTransaction<T>(Future<T> Function(Transaction txn) action, {String? taskName});


  // 차분 동기화용 insertOrUpdate 메서드들
  Future<void> insertOrUpdateEvent(Event event);
  Future<void> insertOrUpdateCategory(Category category);
  Future<void> insertOrUpdateProduct(Product product);
  Future<void> insertOrUpdateSeller(Seller seller);
  Future<void> insertOrUpdatePrepayment(Prepayment prepayment);
  Future<void> insertOrUpdatePrepaymentVirtualProduct(PrepaymentVirtualProduct virtualProduct);
  Future<void> insertOrUpdatePrepaymentProductLink(PrepaymentProductLink link);
  Future<void> insertOrUpdateSalesLog(SalesLog salesLog);

  // 행사 삭제 시 관련 데이터 모두 정리
  Future<void> deleteEventAndAllData(int eventId);

  // 백업용 전체 데이터 조회 메서드들
  Future<List<Event>> getAllEvents();
  Future<List<Category>> getAllCategories();
  Future<List<Product>> getAllProducts();
  Future<List<Seller>> getAllSellers();
  Future<List<SalesLog>> getAllSalesLogs();
  Future<List<Prepayment>> getAllPrepayments();
  Future<List<PrepaymentVirtualProduct>> getAllPrepaymentVirtualProducts();
  Future<List<PrepaymentProductLink>> getAllPrepaymentProductLinks();

  // 추가 백업 데이터 조회 메서드들
  Future<List<Map<String, dynamic>>> getAllSetDiscounts();
  Future<List<Map<String, dynamic>>> getAllChecklistTemplates();
  Future<List<Map<String, dynamic>>> getAllChecklistItems();
  Future<List<Map<String, dynamic>>> getAllRevenueGoals();

  // 누락된 백업 데이터 조회 메서드들
  Future<List<Map<String, dynamic>>> getAllNicknames();
  Future<List<Map<String, dynamic>>> getAllSalesTable();
}

/// DatabaseService Provider
/// - DatabaseServiceImpl 싱글턴 인스턴스를 제공합니다.
final databaseServiceProvider = Provider<DatabaseService>((ref) {
  final service = DatabaseServiceImpl();
  ref.onDispose(() async {
    await service.close();
  });
  return service;
});

// Deprecated provider removed - use databaseServiceProvider.database instead

/// 데이터베이스 작업 큐 아이템
class _DatabaseTask<T> {
  final String id;
  final Future<T> Function() task;
  final Completer<T> completer;
  final DateTime createdAt;

  _DatabaseTask({
    required this.id,
    required this.task,
    required this.completer,
    required this.createdAt,
  });
}

/// 2025년 기준 최적화된 DatabaseService 구현체입니다.
/// - 싱글턴 패턴, 락, DB 초기화/업그레이드, 인덱스 생성 등 고급 기능 포함
/// - 주요 테이블/인덱스/오프라인 큐 구조, 성능/안정성/확장성 최적화
/// - 데이터베이스 접근 큐 시스템으로 동시 접근 문제 해결
class DatabaseServiceImpl implements DatabaseService {
  static Database? _database;
  static const String _databaseName = 'parabara_database.db';
  static const int _databaseVersion = 4; // 체크리스트 템플릿 테이블에 eventId 컬럼 추가
  static final Lock _lock = Lock();
  static bool _isClosing = false;
  static bool _inTransaction = false; // 트랜잭션 중첩 방지

  // 데이터베이스 작업 큐 시스템
  static final Queue<_DatabaseTask<dynamic>> _taskQueue = Queue<_DatabaseTask<dynamic>>();
  static bool _isProcessingQueue = false;
  static int _taskIdCounter = 0;

  // 테이블 이름들 (const로 성능 최적화)
  static const String productsTable = 'products';
  static const String salesTable = 'sales_table';
  static const String prepaymentsTable = 'prepayments';
  static const String salesLogTable = 'sales_log';
  static const String sellersTable = 'sellers';
  static const String eventsTable = 'events';
  static const String setDiscountsTable = 'set_discounts';
  static const String categoriesTable = 'categories';
  static const String checklistTemplatesTable = 'checklist_templates';
  static const String checklistItemsTable = 'checklist_items';

  // Singleton 패턴 (성능 최적화)
  static final DatabaseServiceImpl _instance = DatabaseServiceImpl._internal();
  factory DatabaseServiceImpl() => _instance;
  DatabaseServiceImpl._internal();

  /// DB 인스턴스 반환(싱글턴, 락/재시도/닫힘 처리 포함)
  ///
  /// 반환값: Database 인스턴스
  /// 예외: DB 연결/초기화 실패 시 Exception
  Future<Database> get database async {
    if (_isClosing) {
      await Future.delayed(const Duration(milliseconds: 100));
      _isClosing = false;
    }

    return await _lock.synchronized(() async {
      // 기존 연결이 유효한지 확인
      if (_database != null) {
        try {
          if (_database!.isOpen) {
            // 연결 상태는 isOpen으로만 확인 (성능 최적화)
            return _database!;
          }
        } catch (e) {
          LoggerUtils.logWarning(
            'Database connection check failed, reinitializing: $e',
            tag: 'DatabaseService',
          );
          _database = null;
        }
      }

      _database = await _initDatabase();

      // 데이터베이스 성능 최적화 적용 (중복 실행 방지)
      await DatabaseOptimizer.optimizeDatabase(_database!);
      DatabaseOptimizer.schedulePeriodicOptimization(_database!);

      LoggerUtils.logInfo('Database initialized successfully', tag: 'DatabaseService');
      return _database!;
    });
  }

  /// DB 파일 생성 및 초기화(재시도/싱글턴/버전 관리)
  ///
  /// 반환값: Database 인스턴스
  /// 예외: DB 생성/초기화 실패 시 Exception
  Future<Database> _initDatabase() async {
    // Windows에서 SQLite FFI 초기화
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        final databasesPath = await getDatabasesPath();
        final path = join(databasesPath, _databaseName);

        // 기존 sogom_database.db에서 데이터 마이그레이션 확인
        await _migrateFromOldDatabase(databasesPath, path);

        developer.log(
          '데이터베이스 초기화 중: $path (시도 ${retryCount + 1}/$maxRetries)',
          name: 'DatabaseService',
        );

        return await openDatabase(
          path,
          version: _databaseVersion,
          onCreate: _onCreate,
          onUpgrade: _onUpgrade,
          // 성능 최적화 설정
          readOnly: false,
          singleInstance: true,
        );
      } catch (e, stackTrace) {
        retryCount++;
        developer.log(
          '데이터베이스 초기화 실패 (시도 $retryCount/$maxRetries)',
          error: e,
          stackTrace: stackTrace,
          name: 'DatabaseService',
        );

        if (retryCount >= maxRetries) {
          rethrow;
        }

        // 재시도 전 잠시 대기
        await Future.delayed(Duration(milliseconds: 100 * retryCount));
      }
    }

    throw Exception('데이터베이스 초기화 최종 실패');
  }

  /// 기존 sogom_database.db에서 새 parabara_database.db로 데이터 마이그레이션
  Future<void> _migrateFromOldDatabase(String databasesPath, String newPath) async {
    try {
      final oldPath = join(databasesPath, 'sogom_database.db');
      final oldFile = File(oldPath);
      final newFile = File(newPath);

      // 새 데이터베이스가 이미 존재하거나 기존 데이터베이스가 없으면 마이그레이션 불필요
      if (await newFile.exists() || !await oldFile.exists()) {
        return;
      }

      LoggerUtils.logInfo('기존 데이터베이스에서 마이그레이션 시작: $oldPath -> $newPath', tag: 'DatabaseService');

      // 기존 데이터베이스 열기
      Database? oldDb;
      Database? newDb;

      try {
        oldDb = await openDatabase(oldPath, readOnly: true);

        // 새 데이터베이스 생성
        newDb = await openDatabase(
          newPath,
          version: _databaseVersion,
          onCreate: _onCreate,
        );

        // 테이블별 데이터 마이그레이션
        await _migrateTableData(oldDb, newDb, eventsTable);
        await _migrateTableData(oldDb, newDb, categoriesTable);
        await _migrateTableData(oldDb, newDb, productsTable);
        await _migrateTableData(oldDb, newDb, salesLogTable);
        await _migrateTableData(oldDb, newDb, prepaymentsTable);
        await _migrateTableData(oldDb, newDb, sellersTable);
        await _migrateTableData(oldDb, newDb, 'offline_tasks');

        LoggerUtils.logInfo('데이터베이스 마이그레이션 완료', tag: 'DatabaseService');

        // 기존 데이터베이스를 백업으로 이름 변경
        final backupPath = join(databasesPath, 'sogom_database_backup.db');
        await oldFile.rename(backupPath);
        LoggerUtils.logInfo('기존 데이터베이스를 백업으로 이동: $backupPath', tag: 'DatabaseService');

      } finally {
        await oldDb?.close();
        await newDb?.close();
      }
    } catch (e) {
      LoggerUtils.logError('데이터베이스 마이그레이션 실패', tag: 'DatabaseService', error: e);
      // 마이그레이션 실패해도 앱은 계속 실행 (새 데이터베이스로)
    }
  }

  /// 테이블 데이터 마이그레이션
  Future<void> _migrateTableData(Database oldDb, Database newDb, String tableName) async {
    try {
      // 기존 테이블이 존재하는지 확인
      final tableExists = await oldDb.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName],
      );

      if (tableExists.isEmpty) {
        LoggerUtils.logInfo('테이블이 존재하지 않음, 마이그레이션 건너뜀: $tableName', tag: 'DatabaseService');
        return;
      }

      // 기존 데이터 조회
      final oldData = await oldDb.query(tableName);

      if (oldData.isEmpty) {
        LoggerUtils.logInfo('테이블에 데이터가 없음, 마이그레이션 건너뜀: $tableName', tag: 'DatabaseService');
        return;
      }

      // 새 데이터베이스에 데이터 삽입
      final batch = newDb.batch();
      for (final row in oldData) {
        batch.insert(tableName, row, conflictAlgorithm: ConflictAlgorithm.replace);
      }
      await batch.commit(noResult: true);

      LoggerUtils.logInfo('테이블 마이그레이션 완료: $tableName (${oldData.length}개 레코드)', tag: 'DatabaseService');
    } catch (e) {
      LoggerUtils.logError('테이블 마이그레이션 실패: $tableName', tag: 'DatabaseService', error: e);
    }
  }

  /// DB 최초 생성 시 테이블/인덱스/오프라인 큐 등 전체 구조를 만듭니다.
  ///
  /// [db]: Database 인스턴스
  /// [version]: DB 버전
  /// 예외: 쿼리 실행 실패 시 Exception
  Future<void> _onCreate(Database db, int version) async {
    // Categories 테이블 (새로 추가)
    await db.execute('''
      CREATE TABLE $categoriesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        eventId INTEGER NOT NULL DEFAULT 1,
        sortOrder INTEGER NOT NULL DEFAULT 0,
        color INTEGER NOT NULL DEFAULT 4290756320,
        syncMetadata TEXT,

        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE,
        UNIQUE(name, eventId)
      )
    ''');

    // Products 테이블 (categoryId 컬럼 추가)
    await db.execute('''
      CREATE TABLE $productsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        price INTEGER NOT NULL,
        imagePath TEXT,
        sellerName TEXT,
        lastServicedDate INTEGER,
        isActive INTEGER DEFAULT 1,
        categoryId INTEGER NOT NULL DEFAULT 1,

        eventId INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE,
        FOREIGN KEY (categoryId) REFERENCES $categoriesTable (id),
        UNIQUE(name, eventId, categoryId)
      )
    ''');

    // Sales 테이블
    await db.execute('''
      CREATE TABLE $salesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        sellerName TEXT,
        imagePath TEXT
      )
    ''');

    // Prepayments 테이블 (eventId, orderNumber 컬럼 추가)
    await db.execute('''
      CREATE TABLE $prepaymentsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        buyerName TEXT NOT NULL,
        buyerContact TEXT,
        amount INTEGER NOT NULL,
        pickupDayOfWeek TEXT NOT NULL,
        productNameList TEXT NOT NULL,
        memo TEXT,
        registrationDate TEXT NOT NULL,
        isReceived INTEGER DEFAULT 0,
        registrationActualDayOfWeek INTEGER NOT NULL,
        bankName TEXT,
        email TEXT,
        twitterAccount TEXT,
        registrationTimestamp INTEGER NOT NULL,
        purchasedProductsJson TEXT DEFAULT '{}',
        eventId INTEGER NOT NULL DEFAULT 1,
        orderNumber TEXT,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE
      )
    ''');

    // SalesLog 테이블 (eventId 컬럼 추가)
    await db.execute('''
      CREATE TABLE $salesLogTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        productId INTEGER,
        productName TEXT NOT NULL,
        sellerName TEXT,
        soldPrice INTEGER NOT NULL,
        soldQuantity INTEGER NOT NULL,
        totalAmount INTEGER NOT NULL,
        saleTimestamp INTEGER NOT NULL,
        transactionType TEXT DEFAULT 'SALE',
        batchSaleId TEXT,
        eventId INTEGER NOT NULL DEFAULT 1,
        setDiscountAmount INTEGER NOT NULL DEFAULT 0,
        setDiscountNames TEXT,
        manualDiscountAmount INTEGER NOT NULL DEFAULT 0,
        paymentMethod TEXT,
        FOREIGN KEY (productId) REFERENCES $productsTable (id) ON DELETE SET NULL,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE
      )
    ''');

    // Sellers 테이블 (eventId, sortOrder 컬럼 추가)
    await db.execute('''
      CREATE TABLE $sellersTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        isDefault INTEGER DEFAULT 0,
        eventId INTEGER NOT NULL DEFAULT 1,
        sortOrder INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE,
        UNIQUE(name, eventId)
      )
    ''');



    // [신규] prepayment_product_link 테이블 생성 (virtualProductId로 컬럼명 변경, eventId 추가)
    await db.execute('''
      CREATE TABLE IF NOT EXISTS prepayment_product_link (
        virtualProductId INTEGER NOT NULL,
        productId INTEGER NOT NULL,
        linkedAt TEXT NOT NULL,
        quantity INTEGER DEFAULT 1,
        eventId INTEGER NOT NULL DEFAULT 1,
        PRIMARY KEY (virtualProductId, productId, eventId),
        FOREIGN KEY (virtualProductId) REFERENCES prepayment_virtual_product(id) ON DELETE CASCADE,
        FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE
      )
    ''');



    // [신규] prepayment_virtual_product 테이블 생성 (eventId 컬럼 추가)
    await db.execute('''
      CREATE TABLE IF NOT EXISTS prepayment_virtual_product (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        price REAL NOT NULL DEFAULT 0,
        quantity INTEGER NOT NULL DEFAULT 0,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        eventId INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE
      )
    ''');

    // Events 테이블 생성
    await db.execute('''
      CREATE TABLE $eventsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        imagePath TEXT,
        startDate TEXT NOT NULL,
        endDate TEXT NOT NULL,
        isActive INTEGER DEFAULT 1,
        description TEXT,
        revenueGoalMode TEXT DEFAULT 'overall',
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        ownerUserId TEXT,
        isOwner INTEGER DEFAULT 1,
        realtimeSyncEnabled INTEGER DEFAULT 0,
        syncEnabledByUserId TEXT,
        syncSettingUpdatedAt TEXT
      )
    ''');

    // 닉네임 테이블
    await db.execute('''
      CREATE TABLE nicknames (
        name TEXT PRIMARY KEY,
        sellerId INTEGER,
        profileImagePath TEXT,
        profileImageUrl TEXT,
        FOREIGN KEY (sellerId) REFERENCES sellers(id) ON DELETE CASCADE
      )
    ''');

    // 세트 할인 테이블 (확장된 할인 조건 지원)
    await db.execute('''
      CREATE TABLE $setDiscountsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        discountAmount INTEGER NOT NULL,
        conditionType TEXT NOT NULL DEFAULT 'SetDiscountConditionType.productCombination',
        productIds TEXT NOT NULL DEFAULT '[]',
        minimumAmount INTEGER NOT NULL DEFAULT 0,
        categoryCondition TEXT,
        productGroupCondition TEXT,
        allowMultipleApplications INTEGER DEFAULT 0,
        isActive INTEGER DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        eventId INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE
      )
    ''');

    // 체크리스트 템플릿 테이블 (행사별 관리)
    await db.execute('''
      CREATE TABLE $checklistTemplatesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        eventId INTEGER NOT NULL,
        description TEXT,
        `order` INTEGER NOT NULL DEFAULT 0,
        isActive INTEGER DEFAULT 1,
        createdAt INTEGER,
        updatedAt INTEGER,
        syncMetadata TEXT,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE,
        UNIQUE(title, eventId)
      )
    ''');

    // 체크리스트 아이템 테이블 (행사별 체크 상태)
    await db.execute('''
      CREATE TABLE $checklistItemsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        templateId INTEGER NOT NULL,
        eventId INTEGER NOT NULL,
        isChecked INTEGER DEFAULT 0,
        checkedAt INTEGER,
        updatedAt INTEGER,
        syncMetadata TEXT,
        FOREIGN KEY (templateId) REFERENCES $checklistTemplatesTable (id) ON DELETE CASCADE,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE,
        UNIQUE(templateId, eventId)
      )
    ''');



    // 목표 수익 테이블
    await db.execute('''
      CREATE TABLE revenue_goals (
        id TEXT PRIMARY KEY,
        event_id INTEGER NOT NULL,
        seller_id TEXT,
        date TEXT NOT NULL,
        target_amount REAL NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (event_id) REFERENCES $eventsTable (id) ON DELETE CASCADE,
        UNIQUE(event_id, seller_id, date)
      )
    ''');

    // 세트 할인 거래 테이블
    await db.execute(SetDiscountTransactionRepository.createTableSql);

    // 인덱스 생성
    await _createIndexes(db);
  }

  /// DB 업그레이드 시 새로운 테이블/인덱스를 추가합니다.
  ///
  /// [db]: Database 인스턴스
  /// [oldVersion]: 이전 DB 버전
  /// [newVersion]: 새로운 DB 버전
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    LoggerUtils.logInfo('Database upgrade from version $oldVersion to $newVersion');

    // 버전 1 -> 2: 선입금 테이블의 NULL 허용 필드 수정
    if (oldVersion < 2) {
      await _migratePrepaymentTableToVersion2(db);
    }

    // 버전 2 -> 3: 판매자 테이블에 sortOrder 컬럼 추가
    if (oldVersion < 3) {
      await _addSortOrderToSellersTable(db);
    }

    // 버전 3 -> 4: 체크리스트 템플릿 테이블에 eventId 컬럼 추가
    if (oldVersion < 4) {
      await _migrateChecklistTemplatesToVersion4(db);
    }
  }

  /// 선입금 테이블을 버전 2로 마이그레이션 (NULL 허용 필드 수정)
  Future<void> _migratePrepaymentTableToVersion2(Database db) async {
    try {
      LoggerUtils.logInfo('선입금 테이블 마이그레이션 시작 (v1 -> v2)');

      // 1. 기존 데이터 백업
      await db.execute('CREATE TABLE ${prepaymentsTable}_backup AS SELECT * FROM $prepaymentsTable');

      // 2. 기존 테이블 삭제
      await db.execute('DROP TABLE $prepaymentsTable');

      // 3. 새로운 스키마로 테이블 재생성
      await db.execute('''
        CREATE TABLE $prepaymentsTable (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          buyerName TEXT NOT NULL,
          buyerContact TEXT,
          amount INTEGER NOT NULL,
          pickupDayOfWeek TEXT NOT NULL,
          productNameList TEXT NOT NULL,
          memo TEXT,
          registrationDate TEXT NOT NULL,
          isReceived INTEGER DEFAULT 0,
          registrationActualDayOfWeek INTEGER NOT NULL,
          bankName TEXT,
          email TEXT,
          twitterAccount TEXT,
          registrationTimestamp INTEGER NOT NULL,
          purchasedProductsJson TEXT DEFAULT '{}',
          eventId INTEGER NOT NULL DEFAULT 1,
          orderNumber TEXT,
          FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE
        )
      ''');

      // 4. 데이터 복원 (NULL 값을 빈 문자열로 변환)
      await db.execute('''
        INSERT INTO $prepaymentsTable
        SELECT
          id, buyerName,
          COALESCE(buyerContact, '') as buyerContact,
          amount, pickupDayOfWeek, productNameList, memo, registrationDate, isReceived,
          registrationActualDayOfWeek,
          COALESCE(bankName, '') as bankName,
          COALESCE(email, '') as email,
          twitterAccount, registrationTimestamp, purchasedProductsJson, eventId, orderNumber
        FROM ${prepaymentsTable}_backup
      ''');

      // 5. 백업 테이블 삭제
      await db.execute('DROP TABLE ${prepaymentsTable}_backup');

      LoggerUtils.logInfo('선입금 테이블 마이그레이션 완료 (v1 -> v2)');
    } catch (e) {
      LoggerUtils.logError('선입금 테이블 마이그레이션 실패', error: e);
      rethrow;
    }
  }

  /// 판매자 테이블에 sortOrder 컬럼 추가 (v2 -> v3)
  Future<void> _addSortOrderToSellersTable(Database db) async {
    try {
      LoggerUtils.logInfo('판매자 테이블에 sortOrder 컬럼 추가 시작 (v2 -> v3)');

      // sortOrder 컬럼 추가
      await db.execute('ALTER TABLE $sellersTable ADD COLUMN sortOrder INTEGER NOT NULL DEFAULT 0');

      LoggerUtils.logInfo('판매자 테이블 sortOrder 컬럼 추가 완료 (v2 -> v3)');
    } catch (e) {
      LoggerUtils.logError('판매자 테이블 sortOrder 컬럼 추가 실패', error: e);
      rethrow;
    }
  }

  /// 체크리스트 템플릿 테이블을 버전 4로 마이그레이션 (eventId 컬럼 추가)
  Future<void> _migrateChecklistTemplatesToVersion4(Database db) async {
    try {
      LoggerUtils.logInfo('체크리스트 템플릿 테이블 마이그레이션 시작 (v3 -> v4)');

      // 1. 기존 템플릿 데이터 백업
      await db.execute('CREATE TABLE ${checklistTemplatesTable}_backup AS SELECT * FROM $checklistTemplatesTable');

      // 2. 기존 테이블 삭제
      await db.execute('DROP TABLE $checklistTemplatesTable');

      // 3. 새로운 스키마로 테이블 재생성
      await db.execute('''
        CREATE TABLE $checklistTemplatesTable (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          eventId INTEGER NOT NULL,
          description TEXT,
          `order` INTEGER NOT NULL DEFAULT 0,
          isActive INTEGER DEFAULT 1,
          createdAt INTEGER,
          updatedAt INTEGER,
          syncMetadata TEXT,
          FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE,
          UNIQUE(title, eventId)
        )
      ''');

      // 4. 모든 행사 ID 가져오기
      final List<Map<String, dynamic>> events = await db.query(eventsTable, columns: ['id']);

      if (events.isNotEmpty) {
        // 5. 기존 템플릿을 각 행사별로 복사
        final List<Map<String, dynamic>> oldTemplates = await db.query('${checklistTemplatesTable}_backup');

        for (final event in events) {
          final eventId = event['id'] as int;

          for (final template in oldTemplates) {
            await db.insert(checklistTemplatesTable, {
              'title': template['title'],
              'eventId': eventId,
              'description': template['description'],
              'order': template['order'],
              'isActive': template['isActive'],
              'createdAt': template['createdAt'],
              'updatedAt': template['updatedAt'],
              'syncMetadata': template['syncMetadata'],
            });
          }
        }
      }

      // 6. 백업 테이블 삭제
      await db.execute('DROP TABLE ${checklistTemplatesTable}_backup');

      LoggerUtils.logInfo('체크리스트 템플릿 테이블 마이그레이션 완료 (v3 -> v4)');
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 테이블 마이그레이션 실패', error: e);
      rethrow;
    }
  }

  /// 기존 행사 데이터에 소유권 정보 설정
  /// 앱 시작 시 한 번 호출되어야 함
  Future<void> migrateEventOwnership() async {
    try {
      final db = await database;
      final currentUserId = FirebaseAuth.instance.currentUser?.uid;

      if (currentUserId == null) {
        LoggerUtils.logWarning('사용자가 로그인되지 않아 소유권 마이그레이션을 건너뜁니다', tag: 'DatabaseService');
        return;
      }

      // ownerUserId가 null인 행사들을 현재 사용자 소유로 설정
      await db.execute('''
        UPDATE $eventsTable
        SET ownerUserId = ?, isOwner = 1
        WHERE ownerUserId IS NULL
      ''', [currentUserId]);

      LoggerUtils.logInfo('기존 행사 소유권 마이그레이션 완료', tag: 'DatabaseService');
    } catch (e) {
      LoggerUtils.logError('행사 소유권 마이그레이션 실패', tag: 'DatabaseService', error: e);
    }
  }

  /// 주요 테이블별 인덱스를 생성합니다.
  ///
  /// [db]: Database 인스턴스
  Future<void> _createIndexes(Database db) async {
    // SalesLog 인덱스들 (기존)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_productId ON $salesLogTable (productId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_sellerName ON $salesLogTable (sellerName)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_saleTimestamp ON $salesLogTable (saleTimestamp)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_transactionType ON $salesLogTable (transactionType)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_batchSaleId ON $salesLogTable (batchSaleId)',
    );

    // Sales 인덱스 (기존)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_table_sellerName ON $salesTable (sellerName)',
    );

    // Sellers 인덱스 (기존)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sellers_name ON $sellersTable (name)',
    );

    // 새로운 최적화된 인덱스들
    // Products 테이블 복합 인덱스 (판매자별 재고 조회 최적화)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_products_seller_quantity ON $productsTable (sellerName, quantity)',
    );
    
    // Products 테이블 복합 인덱스 (가격별 정렬 최적화)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_products_price_active ON $productsTable (price, isActive)',
    );
    
    // SalesLog 테이블 복합 인덱스 (판매자별 날짜 범위 조회 최적화)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_seller_timestamp ON $salesLogTable (sellerName, saleTimestamp)',
    );
    
    // SalesLog 테이블 복합 인덱스 (상품별 날짜 범위 조회 최적화)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_product_timestamp ON $salesLogTable (productId, saleTimestamp)',
    );
    
    // Prepayments 테이블 인덱스 (날짜별 조회 최적화)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_prepayments_registration_date ON $prepaymentsTable (registrationDate)',
    );
    
    // Prepayments 테이블 인덱스 (수령 상태별 조회 최적화)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_prepayments_is_received ON $prepaymentsTable (isReceived)',
    );

    // EventId 관련 인덱스들 (행사별 데이터 조회 최적화)
    // Products 테이블 eventId 인덱스
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_products_event_id ON $productsTable (eventId)',
    );

    // Products 테이블 복합 인덱스 (행사별 + 활성 상태)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_products_event_active ON $productsTable (eventId, isActive)',
    );

    // Prepayments 테이블 eventId 인덱스
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_prepayments_event_id ON $prepaymentsTable (eventId)',
    );

    // Prepayments 테이블 복합 인덱스 (행사별 + 수령 상태)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_prepayments_event_received ON $prepaymentsTable (eventId, isReceived)',
    );

    // SalesLog 테이블 eventId 인덱스
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_event_id ON $salesLogTable (eventId)',
    );

    // SalesLog 테이블 복합 인덱스 (행사별 + 날짜)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_event_timestamp ON $salesLogTable (eventId, saleTimestamp)',
    );

    // prepayment_virtual_product 테이블 eventId 인덱스
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_prepayment_virtual_product_event_id ON prepayment_virtual_product (eventId)',
    );
    


    // 체크리스트 템플릿 테이블 인덱스들
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_checklist_templates_order ON $checklistTemplatesTable (`order`)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_checklist_templates_active ON $checklistTemplatesTable (isActive)',
    );

    // 체크리스트 아이템 테이블 인덱스들
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_checklist_items_event_id ON $checklistItemsTable (eventId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_checklist_items_template_id ON $checklistItemsTable (templateId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_checklist_items_template_event ON $checklistItemsTable (templateId, eventId)',
    );

    // Events 테이블 인덱스들
    // 이름별 조회 최적화
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_events_name ON $eventsTable (name)',
    );

    // 날짜별 조회 최적화
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_events_start_date ON $eventsTable (startDate)',
    );

    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_events_end_date ON $eventsTable (endDate)',
    );

    // 상태별 조회 최적화
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_events_is_active ON $eventsTable (isActive)',
    );

    // 생성일별 조회 최적화
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_events_created_at ON $eventsTable (createdAt)',
    );

    // 복합 인덱스 (활성 상태 + 시작일)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_events_active_start_date ON $eventsTable (isActive, startDate)',
    );

    // 세트 할인 테이블 인덱스
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_set_discounts_event_id ON $setDiscountsTable (eventId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_set_discounts_is_active ON $setDiscountsTable (isActive)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_set_discounts_event_active ON $setDiscountsTable (eventId, isActive)',
    );

    // 추가 성능 최적화 인덱스들
    // Products 테이블 - 이름 검색 최적화 (LIKE 쿼리용)
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_products_name_collate ON $productsTable (name COLLATE NOCASE)',
    );

    // registrationTimestamp 컬럼 제거로 인해 해당 인덱스도 제거됨

    // SalesLog 테이블 - 배치 판매 최적화
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_sales_log_batch_timestamp ON $salesLogTable (batchSaleId, saleTimestamp)',
    );

    // Prepayments 테이블 - 구매자 이름 검색 최적화
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_prepayments_buyer_name ON $prepaymentsTable (buyerName COLLATE NOCASE)',
    );

    // Prepayments 테이블 - 등록 시간 정렬 최적화
    await db.execute(
      'CREATE INDEX IF NOT EXISTS index_prepayments_registration_timestamp ON $prepaymentsTable (registrationTimestamp DESC)',
    );

    // 세트 할인 거래 테이블 인덱스들
    for (final indexSql in SetDiscountTransactionRepository.createIndexSqls) {
      await db.execute(indexSql);
    }
  }



  /// 데이터베이스 연결 종료
  Future<void> close() async {
    await _lock.synchronized(() async {
      _isClosing = true;
      if (_database != null && _database!.isOpen) {
        await _database!.close();
        _database = null;
      }
    });
    LoggerUtils.logInfo('Database closed successfully', tag: 'DatabaseService');
  }

  /// 데이터베이스 삭제
  Future<void> deleteDatabase() async {
    await _lock.synchronized(() async {
      final String path = join(await getDatabasesPath(), _databaseName);
      await close();
      if (await File(path).exists()) {
        await databaseFactory.deleteDatabase(path);
      }
    });
  }







  /// [신규] 오프라인 작업 큐 동기화용 raw insert
  Future<void> rawInsert(String table, Map<String, dynamic> data) async {
    final db = await database;
    await db.insert(table, data, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// [신규] 오프라인 작업 큐 동기화용 raw update (id 필수)
  Future<void> rawUpdate(String table, Map<String, dynamic> data) async {
    final db = await database;
    if (!data.containsKey('id')) throw Exception('update에는 id 필수');
    await db.update(table, data, where: 'id = ?', whereArgs: [data['id']]);
  }

  /// [신규] 오프라인 작업 큐 동기화용 raw delete (id 필수)
  Future<void> rawDelete(String table, Map<String, dynamic> data) async {
    final db = await database;
    if (!data.containsKey('id')) throw Exception('delete에는 id 필수');
    await db.delete(table, where: 'id = ?', whereArgs: [data['id']]);
  }

  /// 차분 동기화용 insertOrUpdate 메서드들
  @override
  Future<void> insertOrUpdateEvent(Event event) async {
    await safeTransaction((txn) async {
      final existing = await txn.query(
        eventsTable,
        where: 'id = ?',
        whereArgs: [event.id],
      );

      if (existing.isEmpty) {
        await txn.insert(eventsTable, event.toMap());
      } else {
        await txn.update(
          eventsTable,
          event.toMap(),
          where: 'id = ?',
          whereArgs: [event.id],
        );
      }
    }, taskName: 'insertOrUpdateEvent');
  }

  @override
  Future<void> insertOrUpdateCategory(Category category) async {
    await safeTransaction((txn) async {
      final existing = await txn.query(
        categoriesTable,
        where: 'id = ?',
        whereArgs: [category.id],
      );

      if (existing.isEmpty) {
        await txn.insert(categoriesTable, category.toMap());
      } else {
        await txn.update(
          categoriesTable,
          category.toMap(),
          where: 'id = ?',
          whereArgs: [category.id],
        );
      }
    }, taskName: 'insertOrUpdateCategory');
  }

  @override
  Future<void> insertOrUpdateProduct(Product product) async {
    await safeTransaction((txn) async {
      final existing = await txn.query(
        productsTable,
        where: 'id = ?',
        whereArgs: [product.id],
      );

      if (existing.isEmpty) {
        await txn.insert(productsTable, product.toMap());
      } else {
        await txn.update(
          productsTable,
          product.toMap(),
          where: 'id = ?',
          whereArgs: [product.id],
        );
      }
    }, taskName: 'insertOrUpdateProduct');
  }

  @override
  Future<void> insertOrUpdateSeller(Seller seller) async {
    await safeTransaction((txn) async {
      final existing = await txn.query(
        sellersTable,
        where: 'id = ?',
        whereArgs: [seller.id],
      );

      if (existing.isEmpty) {
        await txn.insert(sellersTable, seller.toMap());
      } else {
        await txn.update(
          sellersTable,
          seller.toMap(),
          where: 'id = ?',
          whereArgs: [seller.id],
        );
      }
    }, taskName: 'insertOrUpdateSeller');
  }

  @override
  Future<void> insertOrUpdatePrepayment(Prepayment prepayment) async {
    await safeTransaction((txn) async {
      final existing = await txn.query(
        prepaymentsTable,
        where: 'id = ?',
        whereArgs: [prepayment.id],
      );

      if (existing.isEmpty) {
        await txn.insert(prepaymentsTable, prepayment.toMap());
      } else {
        await txn.update(
          prepaymentsTable,
          prepayment.toMap(),
          where: 'id = ?',
          whereArgs: [prepayment.id],
        );
      }
    }, taskName: 'insertOrUpdatePrepayment');
  }

  @override
  Future<void> insertOrUpdatePrepaymentVirtualProduct(PrepaymentVirtualProduct virtualProduct) async {
    await safeTransaction((txn) async {
      final existing = await txn.query(
        'prepayment_virtual_product',
        where: 'id = ?',
        whereArgs: [virtualProduct.id],
      );

      if (existing.isEmpty) {
        await txn.insert('prepayment_virtual_product', virtualProduct.toMap());
      } else {
        await txn.update(
          'prepayment_virtual_product',
          virtualProduct.toMap(),
          where: 'id = ?',
          whereArgs: [virtualProduct.id],
        );
      }
    }, taskName: 'insertOrUpdatePrepaymentVirtualProduct');
  }

  @override
  Future<void> insertOrUpdatePrepaymentProductLink(PrepaymentProductLink link) async {
    await safeTransaction((txn) async {
      final existing = await txn.query(
        'prepayment_product_link',
        where: 'virtualProductId = ? AND productId = ?',
        whereArgs: [link.virtualProductId, link.productId],
      );

      if (existing.isEmpty) {
        await txn.insert('prepayment_product_link', link.toDatabaseMap());
      } else {
        await txn.update(
          'prepayment_product_link',
          link.toDatabaseMap(),
          where: 'virtualProductId = ? AND productId = ?',
          whereArgs: [link.virtualProductId, link.productId],
        );
      }
    }, taskName: 'insertOrUpdatePrepaymentProductLink');
  }

  @override
  Future<void> insertOrUpdateSalesLog(SalesLog salesLog) async {
    await safeTransaction((txn) async {
      final existing = await txn.query(
        salesLogTable,
        where: 'id = ?',
        whereArgs: [salesLog.id],
      );

      if (existing.isEmpty) {
        await txn.insert(salesLogTable, salesLog.toMap());
      } else {
        await txn.update(
          salesLogTable,
          salesLog.toMap(),
          where: 'id = ?',
          whereArgs: [salesLog.id],
        );
      }
    }, taskName: 'insertOrUpdateSalesLog');
  }

  @override
  Future<void> deleteEventAndAllData(int eventId) async {
    await safeTransaction((txn) async {
      // 행사와 관련된 모든 데이터 삭제 (순서 중요: 외래키 제약조건 고려)
      await txn.delete(salesLogTable, where: 'eventId = ?', whereArgs: [eventId]);
      await txn.delete('prepayment_product_link', where: 'eventId = ?', whereArgs: [eventId]);
      await txn.delete('prepayment_virtual_product', where: 'eventId = ?', whereArgs: [eventId]);
      await txn.delete(prepaymentsTable, where: 'eventId = ?', whereArgs: [eventId]);
      await txn.delete(productsTable, where: 'eventId = ?', whereArgs: [eventId]);
      await txn.delete(sellersTable, where: 'eventId = ?', whereArgs: [eventId]);
      await txn.delete(categoriesTable, where: 'eventId = ?', whereArgs: [eventId]);

      // 마지막으로 행사 자체 삭제
      await txn.delete(eventsTable, where: 'id = ?', whereArgs: [eventId]);
    }, taskName: 'deleteEventAndAllData');
  }

  /// 앱 실행 시점에 테이블이 없으면 생성하는 안전망 함수
  Future<void> ensurePrepaymentVirtualProductTable() async {
    final db = await database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS prepayment_virtual_product (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        price REAL NOT NULL DEFAULT 0,
        quantity INTEGER NOT NULL DEFAULT 0,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        eventId INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (eventId) REFERENCES $eventsTable (id) ON DELETE CASCADE
      )
    ''');
  }

  /// 데이터베이스 작업을 큐에 추가하여 순차적으로 실행
  Future<T> queueDatabaseTask<T>(Future<T> Function() task, {String? taskName}) async {
    final taskId = '${taskName ?? 'task'}_${++_taskIdCounter}';
    final completer = Completer<T>();

    final dbTask = _DatabaseTask<T>(
      id: taskId,
      task: () async {
        try {
          final result = await task();
          return result;
        } catch (e) {
          LoggerUtils.logError('Database task failed: $taskId', tag: 'DatabaseService', error: e);
          rethrow;
        }
      },
      completer: completer,
      createdAt: DateTime.now(),
    );

    _taskQueue.add(dbTask);
    _processQueue();

    return completer.future;
  }

  // 백업용 전체 데이터 조회 메서드들 구현
  @override
  Future<List<Event>> getAllEvents() async {
    try {
      final db = await database;
      final result = await db.query(eventsTable, orderBy: 'createdAt ASC');

      return result.map((map) => Event.fromMap(map)).toList();
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 행사 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<Category>> getAllCategories() async {
    try {
      final db = await database;
      final result = await db.query(categoriesTable, orderBy: 'eventId ASC, sortOrder ASC');

      return result.map((map) => Category.fromJson({
        'id': map['id'],
        'name': map['name'],
        'sortOrder': map['sortOrder'],
        'eventId': map['eventId'],
        'color': map['color'],
      })).toList();
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 카테고리 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<Product>> getAllProducts() async {
    try {
      final db = await database;
      final result = await db.query(productsTable, orderBy: 'eventId ASC, name ASC');

      return result.map((map) => Product.fromMap(map)).toList();
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 상품 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<Seller>> getAllSellers() async {
    try {
      final db = await database;
      final result = await db.query(sellersTable, orderBy: 'eventId ASC, name ASC');

      return result.map((map) => Seller.fromMap(map)).toList();
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 판매자 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<SalesLog>> getAllSalesLogs() async {
    try {
      final db = await database;
      final result = await db.query(salesLogTable, orderBy: 'eventId ASC, saleTimestamp DESC');

      return result.map((map) => SalesLog.fromMap(map)).toList();
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 판매 기록 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<Prepayment>> getAllPrepayments() async {
    try {
      final db = await database;
      final result = await db.query(prepaymentsTable, orderBy: 'eventId ASC, registrationTimestamp DESC');

      return result.map((map) => Prepayment.fromMap(map)).toList();
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 선결제 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<PrepaymentVirtualProduct>> getAllPrepaymentVirtualProducts() async {
    try {
      final db = await database;

      // 테이블 존재 여부 확인
      final tables = await db.query('sqlite_master', where: 'type = ? AND name = ?', whereArgs: ['table', 'prepayment_virtual_product']);
      if (tables.isEmpty) {
        return [];
      }

      final result = await db.query('prepayment_virtual_product', orderBy: 'eventId ASC, name ASC');

      return result.map((map) => PrepaymentVirtualProduct.fromMap(map)).toList();
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 선결제 가상 상품 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<PrepaymentProductLink>> getAllPrepaymentProductLinks() async {
    try {
      final db = await database;

      // 테이블 존재 여부 확인
      final tables = await db.query('sqlite_master', where: 'type = ? AND name = ?', whereArgs: ['table', 'prepayment_product_link']);
      if (tables.isEmpty) {
        return [];
      }

      final result = await db.query('prepayment_product_link', orderBy: 'eventId ASC');

      return result.map((map) => PrepaymentProductLink.fromMap(map)).toList();
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 선결제 상품 링크 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAllSetDiscounts() async {
    try {
      final db = await database;
      final result = await db.query(setDiscountsTable, orderBy: 'eventId ASC, createdAt ASC');

      LoggerUtils.logInfo('모든 세트할인 조회 완료: ${result.length}개', tag: 'DatabaseService');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 세트할인 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAllChecklistTemplates() async {
    try {
      final db = await database;
      final result = await db.query(checklistTemplatesTable, orderBy: '`order` ASC, createdAt ASC');

      LoggerUtils.logInfo('모든 체크리스트 템플릿 조회 완료: ${result.length}개', tag: 'DatabaseService');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 체크리스트 템플릿 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAllChecklistItems() async {
    try {
      final db = await database;
      final result = await db.query(checklistItemsTable, orderBy: 'eventId ASC, templateId ASC');

      LoggerUtils.logInfo('모든 체크리스트 아이템 조회 완료: ${result.length}개', tag: 'DatabaseService');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 체크리스트 아이템 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAllRevenueGoals() async {
    try {
      final db = await database;

      // 테이블 존재 여부 확인
      final tables = await db.query('sqlite_master', where: 'type = ? AND name = ?', whereArgs: ['table', 'revenue_goals']);
      if (tables.isEmpty) {
        return [];
      }

      // 스키마 컬럼명(event_id)에 맞춰 정렬
      final result = await db.query('revenue_goals', orderBy: 'event_id ASC, date ASC');

      LoggerUtils.logInfo('모든 목표수익 조회 완료: ${result.length}개', tag: 'DatabaseService');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 목표수익 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }



  @override
  Future<List<Map<String, dynamic>>> getAllNicknames() async {
    try {
      final db = await database;

      // 테이블 존재 여부 확인
      final tables = await db.query('sqlite_master', where: 'type = ? AND name = ?', whereArgs: ['table', 'nicknames']);
      if (tables.isEmpty) {
        return [];
      }

      final result = await db.query('nicknames', orderBy: 'name ASC');

      LoggerUtils.logInfo('모든 닉네임 조회 완료: ${result.length}개', tag: 'DatabaseService');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 닉네임 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAllSalesTable() async {
    try {
      final db = await database;

      // 테이블 존재 여부 확인
      final tables = await db.query('sqlite_master', where: 'type = ? AND name = ?', whereArgs: ['table', salesTable]);
      if (tables.isEmpty) {
        return [];
      }

      final result = await db.query(salesTable, orderBy: 'id ASC');

      LoggerUtils.logInfo('모든 판매 데이터 조회 완료: ${result.length}개', tag: 'DatabaseService');
      return result;
    } catch (e, stackTrace) {
      LoggerUtils.logError('모든 판매 데이터 조회 실패', tag: 'DatabaseService', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  /// 큐에 있는 작업들을 순차적으로 처리
  static void _processQueue() async {
    if (_isProcessingQueue || _taskQueue.isEmpty) return;

    _isProcessingQueue = true;

    try {
      while (_taskQueue.isNotEmpty) {
        final task = _taskQueue.removeFirst();

        try {
          LoggerUtils.logDebug('Processing database task: ${task.id}', tag: 'DatabaseService');
          final result = await task.task();
          task.completer.complete(result);
        } catch (e) {
          task.completer.completeError(e);
        }
      }
    } finally {
      _isProcessingQueue = false;
    }
  }

  /// 지수 백오프 재시도 로직
  Future<T> _retryWithExponentialBackoff<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(milliseconds: 100),
    double backoffMultiplier = 2.0,
    String? operationName,
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempt++;

        // 재시도 불가능한 오류인지 확인
        if (_isNonRetryableError(e) || attempt >= maxRetries) {
          LoggerUtils.logError(
            'Operation failed after $attempt attempts: ${operationName ?? 'unknown'}',
            tag: 'DatabaseService',
            error: e,
          );
          rethrow;
        }

        LoggerUtils.logWarning(
          'Operation failed (attempt $attempt/$maxRetries): ${operationName ?? 'unknown'}, retrying in ${currentDelay.inMilliseconds}ms',
          tag: 'DatabaseService',
          error: e,
        );

        await Future.delayed(currentDelay);
        currentDelay = Duration(milliseconds: (currentDelay.inMilliseconds * backoffMultiplier).round());
      }
    }

    throw Exception('Operation failed after $maxRetries attempts');
  }

  /// 재시도 불가능한 오류인지 확인
  bool _isNonRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // 재시도 불가능한 오류 패턴들
    final nonRetryablePatterns = [
      'constraint',
      'foreign key',
      'unique',
      'not null',
      'syntax error',
      'no such table',
      'no such column',
    ];

    return nonRetryablePatterns.any((pattern) => errorString.contains(pattern));
  }

  /// 안전한 트랜잭션 실행 (중첩 방지 + 큐 시스템 + 재시도 로직)
  Future<T> safeTransaction<T>(Future<T> Function(Transaction txn) action, {String? taskName}) async {
    return queueDatabaseTask<T>(() async {
      return _retryWithExponentialBackoff<T>(() async {
        if (_inTransaction) {
          throw Exception(
            'Transaction already in progress. Nested transactions are not allowed.',
          );
        }

        final db = await database;
        _inTransaction = true;
        try {
          return await db.transaction<T>((txn) async {
            return await action(txn);
          });
        } finally {
          _inTransaction = false;
        }
      }, operationName: taskName ?? 'transaction');
    }, taskName: taskName ?? 'transaction');
  }


}
